from quant.tdx_data import get_data

# 正确的数据，用于测试，不要修改
test_samples = [
    {'code': 'sh688583', 'begin': 20250618, 'end': 20250620, 'nfq': [110.1, 81.98, 76.1], 'qfq': [84.269, 81.98, 76.1], 'hfq': [110.1, 107.109, 99.427]},
    {'code': 'sh603072', 'begin': 20250310, 'end': 20250312, 'nfq': [38.62, 38.88, 38.3], 'qfq': [38.47, 38.88, 38.3], 'hfq': [38.62, 39.032, 38.449]},
    {'code': 'sh601916', 'begin': 20200709, 'end': 20200713, 'nfq': [5.04, 4.51, 4.49], 'hfq': [5.04, 4.736, 4.715]},
    {'code': 'sh601916', 'begin': 20200729, 'end': 20200802,'nfq': [4.16, 4.12, 4.12],'hfq': [4.368, 4.326, 4.326]},
    {'code': 'sh601916', 'begin': 20230518, 'end': 20230522,'nfq': [3.24, 2.99, 2.96],'hfq': [3.55, 3.504, 3.468]},
    {'code': 'sh601916', 'begin': 20230614, 'end': 20230628,'nfq': [2.74, 2.54, 2.58],'hfq': [3.211, 3.168, 3.218]},
    {'code': 'sh601916', 'begin': 20240625, 'end': 20240627,'nfq': [2.82, 2.69, 2.73],'hfq': [3.518, 3.563, 3.616]},
    {'code': 'sh601916', 'begin': 20250626, 'end': 20250630,'nfq': [3.68, 3.41, 3.39], 'qfq': [3.524, 3.41, 3.39],'hfq': [4.874, 4.716, 4.689]},
]


if __name__ == '__main__':
    for sample_dict in test_samples:
        code = sample_dict['code']
        begin = sample_dict['begin']
        end = sample_dict['end']

        # 不复权
        df_no_fq = get_data(code, fq=0, begin=begin, end=end)
        no_fq = [round(i, 3) for i in df_no_fq["close"].to_list()]
        if 'nfq' in sample_dict:
            assert no_fq == sample_dict['nfq'], f"不复权数据不正确: {no_fq} != {sample_dict['nfq']}"
        else:
            print(f"'nfq': {no_fq}")

        # 前复权
        df_qfq = get_data(code, fq=1, begin=begin, end=end)
        qfq = [round(i, 3) for i in df_qfq["close"].to_list()]
        if 'qfq' in sample_dict:
            assert qfq == sample_dict['qfq'], f"前复权数据不正确: {qfq} != {sample_dict['qfq']}"
        else:
            print(f"'qfq': {qfq}")

        # 后复权
        df_hfq = get_data(code, fq=2, begin=begin, end=end)
        hfq = [round(i, 3) for i in df_hfq["close"].to_list()]
        if 'hfq' in sample_dict:
            assert hfq == sample_dict['hfq'], f"后复权数据不正确: {hfq} != {sample_dict['hfq']}"
        else:
            print(f"'hfq': {hfq}")

        if 
        print(f"测试数据 {code} 通过")